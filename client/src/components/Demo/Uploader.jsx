import { useState } from "react";
import ipfs from "../../ipfs";

function Uploader({ onUpload }) {
  const [file, setFile] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [hash, setHash] = useState("");
  const [error, setError] = useState("");

  const handleFileChange = (e) => {
    setFile(e.target.files[0]);
    setHash("");
    setError("");
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!file) return;

    setUploading(true);
    setError("");

    try {
      const reader = new window.FileReader();
      reader.readAsArrayBuffer(file);
      reader.onloadend = async () => {
        try {
          const buffer = Buffer.from(reader.result);
          const result = await ipfs.add(buffer);
          setHash(result.path);
          if (onUpload) onUpload(result.path);
          setUploading(false);
        } catch (uploadError) {
          console.error("IPFS upload error:", uploadError);
          setError("Failed to upload to IPFS. Please check your IPFS connection.");
          setUploading(false);
        }
      };
      reader.onerror = () => {
        setError("Failed to read file.");
        setUploading(false);
      };
    } catch (err) {
      console.error("File reading error:", err);
      setError("Failed to process file.");
      setUploading(false);
    }
  };

  return (
    <div>
      <h3>IPFS Image Uploader</h3>
      <form onSubmit={handleSubmit}>
        <div style={{ marginBottom: "1em" }}>
          <input
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            disabled={uploading}
            style={{ marginRight: "1em" }}
          />
          <button type="submit" disabled={!file || uploading}>
            {uploading ? "Uploading..." : "Upload to IPFS"}
          </button>
        </div>
      </form>

      {error && (
        <div style={{ color: "red", marginTop: "1em" }}>
          <strong>Error:</strong> {error}
        </div>
      )}

      {hash && (
        <div style={{ marginTop: "1em" }}>
          <div style={{ marginBottom: "1em" }}>
            <strong>Upload Successful!</strong>
          </div>
          <div style={{ marginBottom: "1em" }}>
            <strong>IPFS Hash:</strong> <code style={{ background: "#f0f0f0", padding: "2px 4px" }}>{hash}</code>
          </div>
          <div style={{ marginBottom: "1em" }}>
            <strong>IPFS URL:</strong>
            <a
              href={`https://ipfs.io/ipfs/${hash}`}
              target="_blank"
              rel="noopener noreferrer"
              style={{ marginLeft: "0.5em" }}
            >
              https://ipfs.io/ipfs/{hash}
            </a>
          </div>
          <div>
            <img
              src={`https://ipfs.io/ipfs/${hash}`}
              alt="Uploaded to IPFS"
              style={{
                maxWidth: "400px",
                maxHeight: "400px",
                border: "1px solid #ddd",
                borderRadius: "4px"
              }}
              onError={(e) => {
                e.target.style.display = "none";
                e.target.nextSibling.style.display = "block";
              }}
            />
            <div style={{ display: "none", color: "orange" }}>
              Image preview failed to load. The file was uploaded successfully, but the IPFS gateway might be slow.
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default Uploader;