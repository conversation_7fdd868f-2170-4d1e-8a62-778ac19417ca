import { useState } from "react";
import ipfs from "../../ipfs";

function Uploader({ onUpload }) {
  const [file, setFile] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [hash, setHash] = useState("");

  const handleFileChange = (e) => {
    setFile(e.target.files[0]);
    setHash("");
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!file) return;
    setUploading(true);
    try {
      const reader = new window.FileReader();
      reader.readAsArrayBuffer(file);
      reader.onloadend = async () => {
        const buffer = Buffer.from(reader.result);
        const result = await ipfs.add(buffer);
        setHash(result.path);
        if (onUpload) onUpload(result.path);
        setUploading(false);
      };
    } catch (err) {
      setUploading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <input
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        disabled={uploading}
      />
      <button type="submit" disabled={!file || uploading}>
        {uploading ? "Uploading..." : "Upload"}
      </button>
      {hash && (
          <div>
            Uploaded! IPFS Hash: <code>{hash}</code>
            <div style={{ marginTop: "1em" }}>
              <img
                  src={`https://ipfs.io/ipfs/${hash}`}
                  alt="Uploaded"
                  style={{ maxWidth: "300px", maxHeight: "300px" }}
              />
            </div>
          </div>
      )}
    </form>
  );
}

export default Uploader;