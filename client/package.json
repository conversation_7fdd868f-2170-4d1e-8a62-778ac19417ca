{"name": "truffle-client", "version": "0.1.0", "private": true, "scripts": {"build": "webpack", "start": "webpack serve"}, "dependencies": {"buffer": "^6.0.3", "ipfs-http-client": "^60.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "web3": "^1.8.2"}, "devDependencies": {"@babel/preset-react": "^7.18.6", "babel-loader": "^9.1.2", "crypto-browserify": "^3.12.1", "css-loader": "^6.7.3", "eslint": "^8.34.0", "eslint-config-react-app": "^7.0.1", "eslint-webpack-plugin": "^4.0.0", "html-webpack-plugin": "^5.5.0", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "style-loader": "^3.3.1", "url": "^0.11.4", "util": "^0.12.5", "webpack": "^5.75.0", "webpack-cli": "^5.0.1", "webpack-dev-server": "^4.11.1"}, "eslintConfig": {"extends": ["react-app"]}}